
API Log viewer   tag: latest commit: a439c3e build time: 20240129_080840
Microservice Log
Gateway Log
Routing Details
total hit:24

Microservice Result for X-B3-TraceId: c184203dffd6001d
Time 	Messages 	Request UID 	Span ID 	App name 	Thread Name 	Logger name 	Level
2025-11-03 14:13:41.486 	

-->(api): 

		c184203dffd6001d 	api-framework-netflix-zuul 	http-nio-8080-exec-46 	com.scb.api.common.framework.config2.ServiceApiConfig 	INFO
2025-11-03 14:13:41.487 	

Incoming requestUID 25110314134132462287 - POST http://startbiz-api.scb.co.th/scb/rest/ent-api/v1/support/utility/proxy with QueryString = profile=DBD

		f343020c48c748fc 	api-framework-netflix-zuul 	http-nio-8080-exec-46 	com.scb.api.common.netflix.zuul.filters.ZuulPreFilter 	INFO
2025-11-03 14:13:41.487 	

-->(ribbon): 

		f343020c48c748fc 	api-framework-netflix-zuul 	http-nio-8080-exec-46 	com.scb.api.common.netflix.zuul.custom.CustomRibbonLoadBalancingHttpClient 	INFO
2025-11-03 14:13:41.489 	

Validation Interceptor ==> Pre-handle

		f343020c48c748fc 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.common.framework.interceptor.CommonValidationInterceptor 	INFO
2025-11-03 14:13:41.490 	

[100.96.1.33] POST http://100.96.1.33:8080/scb/rest/ent-api/v1/support/utility/proxy?profile=DBD INITIATED...

		e22485025f01f9d1 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.ent.support.util.proxy.controller.ProxyAPI 	INFO
2025-11-03 14:13:41.490 	

unescapeString: {"OrganizationJuristicID": "0505568016173"}

		e22485025f01f9d1 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.ent.support.util.proxy.impl.ProxyAPIImpl 	INFO
2025-11-03 14:13:41.491 	

Query Redis key: scb.security_injector.DBD.OAuth2.accessToken

		e22485025f01f9d1 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.ent.support.util.proxy.util.RedisUtil 	INFO
2025-11-03 14:13:41.491 	

get profile vault = DBD

		e22485025f01f9d1 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.ent.support.util.proxy.config.VaultInitial 	INFO
2025-11-03 14:13:41.491 	

replace vault key = \$\{vault.authen_key}

		e22485025f01f9d1 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.ent.support.util.proxy.util.MapSettingForRedisVaults 	INFO
2025-11-03 14:13:41.524 	

Query Redis key: scb.security_injector.DBD.OAuth2.tokenType

		e22485025f01f9d1 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.ent.support.util.proxy.util.RedisUtil 	INFO
2025-11-03 14:13:41.524 	

Got Redis value from =====> Redis server : null

		e22485025f01f9d1 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.ent.support.util.proxy.util.RedisUtil 	INFO
2025-11-03 14:13:41.556 	

Got Redis value from =====> Redis server : null

		e22485025f01f9d1 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.ent.support.util.proxy.util.RedisUtil 	INFO
2025-11-03 14:13:41.557 	

Calling to GW Proxy(https://intapigw.scb.co.th:8448/scb/rest/ent-api/reverse/proxy) with requestUID 25110314134132462287

		e22485025f01f9d1 	support-util-proxy 	hystrix-GatewayProxyClient-199 	com.scb.api.ent.support.util.proxy.service.GatewayProxyClient 	INFO
2025-11-03 14:13:42.101 	

Invalid cookie header: "Set-Cookie: visid_incap_3048096=UCjHnwvmR1el4KoJmLph2CRWCGkAAAAAQUIPAAAAAACFi6yIye5TZi1hgx+Q0nmU; expires=Mon, 02 Nov 2026 15:40:01 GMT; HttpOnly; Path=/scb/rest/ent-api/reverse; Domain=intapigw.scb.co.th". Invalid 'expires' attribute: Mon, 02 Nov 2026 15:40:01 GMT

		4d9d04884f92345f 	support-util-proxy 	hystrix-GatewayProxyClient-199 	org.apache.http.client.protocol.ResponseProcessCookies 	WARN
2025-11-03 14:13:42.102 	

set redis name, token key: scb.security_injector.DBD.OAuth2.data.accessToken, token value: 55b03m59924Vj5dt9mJY518-2jH57s55fSZW754H99Yj41JZ0AH-01-0X-n5Zp03GijvPt279553hPj1 , expireTime: 25 MINUTES

		e22485025f01f9d1 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.ent.support.util.proxy.util.RedisUtil 	INFO
2025-11-03 14:13:42.134 	

set redis name, token key: scb.security_injector.DBD.OAuth2.data.tokenType, token value: Bearer , expireTime: 25 MINUTES

		e22485025f01f9d1 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.ent.support.util.proxy.util.RedisUtil 	INFO
2025-11-03 14:13:42.166 	

replace redis key = \$\{redis.data.tokenType}

		e22485025f01f9d1 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.ent.support.util.proxy.util.MapSettingForRedisVaults 	INFO
2025-11-03 14:13:42.166 	

replace redis key = \$\{redis.data.accessToken}

		e22485025f01f9d1 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.ent.support.util.proxy.util.MapSettingForRedisVaults 	INFO
2025-11-03 14:13:42.166 	

Calling to GW Proxy(https://intapigw.scb.co.th:8448/scb/rest/ent-api/reverse/proxy) with requestUID 25110314134132462287

		e22485025f01f9d1 	support-util-proxy 	hystrix-GatewayProxyClient-196 	com.scb.api.ent.support.util.proxy.service.GatewayProxyClient 	INFO
2025-11-03 14:13:43.716 	

Invalid cookie header: "Set-Cookie: visid_incap_3048096=RWEJ4vx3SMGTAxUThUmMRiZWCGkAAAAAQUIPAAAAAACsGRqOKDLaZuGkkWdNIUWQ; expires=Mon, 02 Nov 2026 16:32:50 GMT; HttpOnly; Path=/scb/rest/ent-api/reverse; Domain=intapigw.scb.co.th". Invalid 'expires' attribute: Mon, 02 Nov 2026 16:32:50 GMT

		ffca0676237d79c8 	support-util-proxy 	hystrix-GatewayProxyClient-196 	org.apache.http.client.protocol.ResponseProcessCookies 	WARN
2025-11-03 14:13:43.717 	

<--(ribbon): 

		f343020c48c748fc 	api-framework-netflix-zuul 	http-nio-8080-exec-46 	com.scb.api.common.netflix.zuul.custom.CustomRibbonLoadBalancingHttpClient 	INFO
2025-11-03 14:13:43.717 	

Parsed JSON data2

traceId: c184203dffd6001d
requestPath: /scb/rest/ent-api/v1/support/utility/proxy
responsePayload:
{
    "status": {
        "code": "1000",
        "description": "Success"
    },
    "data": {
        "cd:OrganizationJuristicPerson": {
            "cd:OrganizationJuristicID": "0505568016173",
            "cd:OrganizationOldJuristicID": null,
            "cd:OrganizationJuristicNameTH": "บริษัท นิชา เวลเนสแอนด์บิวตี้ เซ็นเตอร์ จำกัด",
            "cd:OrganizationJuristicNameEN": "NICHA WELLNESS AND BEAUTY CENTER CO., LTD.",
            "cd:OrganizationJuristicType": "บริษัทจำกัด",
            "cd:OrganizationJuristicRegisterDate": "20250801",
            "cd:OrganizationJuristicStatus": "ยังดำเนินกิจการอยู่",
            "cd:OrganizationJuristicObjective": [
                {
                    "td:JuristicObjective": "R",
                    "td:JuristicObjectiveCode": "20232",
                    "td:JuristicObjectiveTextTH": "การผลิตเครื่องหอม เครื่องสำอาง และผลิตภัณฑ์ในห้องน้ำ",
                    "td:JuristicObjectiveTextEN": "Manufacture of perfumes, cosmetics and toilet preparations"
                },
                {
                    "td:JuristicObjective": "F",
                    "td:JuristicObjectiveCode": null,
                    "td:JuristicObjectiveTextTH": null,
                    "td:JuristicObjectiveTextEN": null
                }
            ],
            "cd:OrganizationJuristicObjectiveItems": "23",
            "cd:OrganizationJuristicObjectivePages": 2,
            "cd:OrganizationJuristicRegisterCapital": "1000000.00",
            "cd:OrganizationJuristicPaidUpCapital": null,
            "cd:OrganizationJuristicPersonList": [
                {
                    "td:JuristicPersonSequence": 1,
                    "td:JuristicPersonType": "กรรมการ",
                    "td:JuristicPerson": {
                        "cd:PersonNameTH": {
                            "cd:PersonNameTitleTextTH": "นางสาว",
                            "cd:PersonFirstNameTH": "ภรณ์พิชพรรณ์",
                            "cd:PersonMiddleNameTH": "",
                            "cd:PersonLastNameTH": "วุฒไชยา"
                        }
                    },
                    "td:JuristicPersonInvestType": "",
                    "td:JuristicPersonInvestAmount": ""
                }
            ],
            "cd:OrganizationJuristicBranchName": "สำนักงานใหญ่",
            "cd:OrganizationJuristicAddress": {
                "cr:AddressType": {
                    "cd:Address": "438/1  หมู่ที่ 2",
                    "cd:Building": "",
                    "cd:RoomNo": "",
                    "cd:Floor": "",
                    "cd:AddressNo": "438/1",
                    "cd:Moo": "2",
                    "cd:Yaek": "",
                    "cd:Soi": null,
                    "cd:Trok": "",
                    "cd:Village": "",
                    "cd:Road": null,
                    "cd:CitySubDivision": {
                        "cr:CitySubDivisionCode": "50140200",
                        "cr:CitySubDivisionTextTH": "สันทรายน้อย"
                    },
                    "cd:City": {
                        "cr:CityCode": "5014",
                        "cr:CityTextTH": "สันทราย"
                    },
                    "cd:CountrySubDivision": {
                        "cr:CountrySubDivisionCode": "TH-50",
                        "cr:CountrySubDivisionTextTH": "เชียงใหม่"
                    }
                }
            },
            "cd:OrganizationJuristicPersonDescription": [
                {
                    "cd:OrganizationJuristicPersonDescriptionSequence": 1,
                    "cd:OrganizationJuristicPersonDescriptionType": "อำนาจกรรมการ",
                    "cd:OrganizationJuristicPersonDescriptionDetail": "นางสาวภรณ์พิชพรรณ์ วุฒไชยา ลงลายมือชื่อและประทับตราสำคัญของบริษัท"
                }
            ],
            "td:FinancialSubmitRecord": "",
            "td:DigitalIDFlag": "Y"
        }
    }
}
requestQuery: profile=DBD
isFileUpload: 
responseCode: 200
guessRoute: /scb/rest/ent-api/v1/support/utility/proxy

requestUID: 25110314134132462287
requestMethod: POST
isContainSensitiveInformation: 
sourceSystem: OBJU
processingTime: 2230
routedServiceId: support-util-proxy
requestPayload:
{
    "body": "{\"OrganizationJuristicID\": \"0505568016173\"}",
    "method": "POST",
    "header": {},
    "url": "https://api.dbd.go.th/text/JuristicPerson/v1/InquiryOJPbyID"
}

	25110314134132462287 	c184203dffd6001d 	api-framework-netflix-zuul 	http-nio-8080-exec-46 	com.scb.api.common.netflix.zuul.elk.ZuulStat 	INFO
2025-11-03 14:13:43.717 	

<--(api): 

		c184203dffd6001d 	api-framework-netflix-zuul 	http-nio-8080-exec-46 	com.scb.api.common.framework.config2.ServiceApiConfig 	INFO
2025-11-03 14:13:43.717 	

Validation Interceptor ==> After-completion

		f343020c48c748fc 	support-util-proxy 	http-nio-8080-exec-9 	com.scb.api.common.framework.interceptor.CommonValidationInterceptor 	INFO

