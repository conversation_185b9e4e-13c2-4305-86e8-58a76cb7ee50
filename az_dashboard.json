{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "This is a modern 'Namespaces View' dashboard for your Kubernetes cluster(s). Made for kube-prometheus-stack and take advantage of the latest Grafana features. GitHub repository: https://github.com/dotdc/grafana-dashboards-kubernetes", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 53, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 38, "panels": [], "title": "Overview", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"decimals": 2, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green"}, {"color": "orange", "value": 50}, {"color": "red", "value": 70}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 1}, "id": 46, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto", "text": {}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": false, "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=~\"$namespace\", image!=\"\", cluster=\"$cluster\"}[$__rate_interval])) / sum(kube_node_status_capacity{resource=\"cpu\",cluster=\"$cluster\"})", "instant": true, "interval": "", "legendFormat": "", "range": false, "refId": "A"}], "title": "Namespace(s) usage on total cluster CPU in %", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"decimals": 2, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green"}, {"color": "orange", "value": 50}, {"color": "red", "value": 70}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 1}, "id": 48, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto", "text": {}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "exemplar": true, "expr": "sum(container_memory_working_set_bytes{namespace=~\"$namespace\", image!=\"\", cluster=\"$cluster\"}) / sum(kube_node_status_capacity{resource=\"memory\",cluster=\"$cluster\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Namespace(s) usage on total cluster RAM in %", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 1}, "id": 32, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_pod_info{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Running Pods", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_service_info{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Services", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_ingress_info{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Ingresses", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_deployment_labels{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Deployments", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_statefulset_labels{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Statefulsets", "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_daemonset_labels{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Daemonsets", "refId": "F"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_persistentvolumeclaim_info{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Persistent Volume Claims", "refId": "G"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_hpa_labels{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Horizontal Pod Autoscalers", "refId": "H"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_configmap_info{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Configmaps", "refId": "I"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_secret_info{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Secrets", "refId": "J"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_networkpolicy_labels{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Network Policies", "refId": "K"}], "title": "Kubernetes Resource Count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)"}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 8}, "id": 62, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=~\"$namespace\", image!=\"\", cluster=\"$cluster\"}[$__rate_interval]))", "interval": "", "legendFormat": "Real", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{namespace=~\"$namespace\", resource=\"cpu\", cluster=\"$cluster\"})", "hide": false, "legendFormat": "Requests", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_limits{namespace=~\"$namespace\", resource=\"cpu\", cluster=\"$cluster\"})", "hide": false, "legendFormat": "Limits", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(machine_cpu_cores{cluster=\"$cluster\"})", "hide": false, "legendFormat": "Cluster Total", "range": true, "refId": "D"}], "title": "Namespace(s) CPU Usage in cores", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)"}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 8}, "id": 64, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": true, "expr": "sum(container_memory_working_set_bytes{namespace=~\"$namespace\", image!=\"\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Real", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{namespace=~\"$namespace\", resource=\"memory\", cluster=\"$cluster\"})", "hide": false, "legendFormat": "Requests", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(kube_pod_container_resource_limits{namespace=~\"$namespace\", resource=\"memory\", cluster=\"$cluster\"})", "hide": false, "legendFormat": "Limits", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(machine_memory_bytes{cluster=\"$cluster\"})", "hide": false, "legendFormat": "Cluster Total", "range": true, "refId": "D"}], "title": "Namespace(s) RAM Usage in bytes", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 19, "panels": [], "title": "Headlines (CPU and Memory)", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 13}, "id": 1, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=~\"$namespace\", image!=\"\", cluster=\"$cluster\"}[$__rate_interval])) / sum(kube_pod_container_resource_requests{namespace=~\"$namespace\", resource=\"cpu\", cluster=\"$cluster\"})", "format": "time_series", "instant": true, "refId": "A"}], "title": "CPU Utilisation (from requests)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 13}, "id": 85, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=~\"$namespace\", image!=\"\", cluster=\"$cluster\"}[$__rate_interval])) / sum(kube_pod_container_resource_limits{namespace=~\"$namespace\", resource=\"cpu\", cluster=\"$cluster\"})", "format": "time_series", "instant": true, "refId": "A"}], "title": "CPU Utilisation (from limits)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 13}, "id": 3, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(container_memory_working_set_bytes{job=\"cadvisor\", cluster=\"$cluster\", namespace=~\"$namespace\",container!=\"\", image!=\"\"}) / sum(kube_pod_container_resource_requests{job=\"kube-state-metrics\", cluster=\"$cluster\", namespace=~\"$namespace\", resource=\"memory\"})", "format": "time_series", "instant": true, "refId": "A"}], "title": "Memory Utilisation (from requests)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 13}, "id": 4, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(container_memory_working_set_bytes{namespace=~\"$namespace\", image!=\"\", cluster=\"$cluster\"}) / sum(kube_pod_container_resource_limits{namespace=~\"$namespace\", resource=\"memory\", cluster=\"$cluster\"})", "format": "time_series", "instant": true, "refId": "A"}], "title": "Memory Utilisation (from limits)", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 40, "panels": [], "title": "CPU", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "decimals": 2, "displayName": "", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "displayName", "value": "Time"}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "CPU Usage"}, {"id": "unit", "value": "short"}, {"id": "custom.align"}, {"id": "noValue", "value": "0.00"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Running Pods"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "displayName", "value": "CPU Requests"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #D"}, "properties": [{"id": "displayName", "value": "CPU Requests %"}, {"id": "unit", "value": "percentunit"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}, {"id": "noValue", "value": "0.00%"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #E"}, "properties": [{"id": "displayName", "value": "CPU Limits"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #F"}, "properties": [{"id": "displayName", "value": "CPU Limits %"}, {"id": "unit", "value": "percentunit"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}, {"id": "noValue", "value": "0.00%"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "owner_name"}, "properties": [{"id": "displayName", "value": "Workload"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "links", "value": [{"targetBlank": true, "title": "Drill down", "url": "/d/3151475894614845ba54456099696738/k8s-resources-workload?var-datasource=$datasource&var-cluster=$cluster&var-namespace=$namespace&from=$__from&to=$__to&var-workload=$__cell&var-type=$__cell_2"}]}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "owner_kind"}, "properties": [{"id": "displayName", "value": "Workload Type"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "decimals", "value": 0}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Workload"}, "properties": [{"id": "custom.width", "value": 222}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 17}, "id": 91, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "CPU Requests %"}]}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": false, "expr": "count(\r\n    label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "__auto", "range": false, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": false, "expr": "sum(\r\n  rate(container_cpu_usage_seconds_total{namespace=~\"$namespace\", image!=\"\", cluster=\"$cluster\"}[$__rate_interval])\r\n* on(pod,namespace)\r\n  group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": false, "expr": "sum(\r\n  kube_pod_container_resource_requests{cluster=\"$cluster\", namespace=~\"$namespace\", resource=\"cpu\"}\r\n* on(namespace,pod)\r\n  group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": false, "expr": "sum(\r\n  rate(container_cpu_usage_seconds_total{namespace=~\"$namespace\", image!=\"\", cluster=\"$cluster\"}[$__rate_interval])\r\n* on(namespace,pod)\r\n  group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n/sum(\r\n  kube_pod_container_resource_requests{ cluster=\"$cluster\", namespace=~\"$namespace\", resource=\"cpu\"}\r\n* on(namespace,pod)\r\n  group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": false, "expr": "sum(\r\n  kube_pod_container_resource_limits{cluster=\"$cluster\", namespace=~\"$namespace\", resource=\"cpu\"}\r\n* on(namespace,pod)\r\n  group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) \r\n  group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": false, "expr": "sum(\r\n  rate(container_cpu_usage_seconds_total{namespace=~\"$namespace\", image!=\"\", cluster=\"$cluster\"}[$__rate_interval])\r\n* on(namespace,pod)\r\n  group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n/sum(\r\n  kube_pod_container_resource_limits{cluster=\"$cluster\", namespace=~\"$namespace\", resource=\"cpu\"}\r\n* on(namespace,pod)\r\n  group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "F"}], "title": "CPU Quota", "transformations": [{"id": "merge", "options": {"reducers": []}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "CPU CORES", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 29, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=~\"$namespace\", image!=\"\", pod=~\"${created_by}.*\", cluster=\"$cluster\"}[$__rate_interval])) by (pod)", "interval": "$resolution", "legendFormat": "{{ pod }}", "range": true, "refId": "A"}], "title": "CPU usage by Pod", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "No data is generally a good thing here.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "SECONDS", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 52, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 87, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "scbprometheus001dev"}, "editorMode": "code", "expr": "sum(rate(container_cpu_cfs_throttled_periods_total{namespace=~\"$namespace\", image!=\"\", pod=~\"${created_by}.*\", cluster=\"$cluster\"}[$__rate_interval])) by (pod) > 0", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "CPU Throttled seconds by pod", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 32}, "id": 86, "panels": [], "title": "Memory", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "decimals": 2, "displayName": "", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "displayName", "value": "Time"}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "Running Pods"}, {"id": "unit", "value": "short"}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Memory Usage"}, {"id": "unit", "value": "bytes"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}, {"id": "noValue", "value": "0.00"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "displayName", "value": "Memory Requests"}, {"id": "unit", "value": "bytes"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #D"}, "properties": [{"id": "displayName", "value": "Memory Requests %"}, {"id": "unit", "value": "percentunit"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}, {"id": "noValue", "value": "0.00%"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #E"}, "properties": [{"id": "displayName", "value": "Memory Limits"}, {"id": "unit", "value": "bytes"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #F"}, "properties": [{"id": "displayName", "value": "Memory Limits %"}, {"id": "unit", "value": "percentunit"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}, {"id": "noValue", "value": "0.00%"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "owner_name"}, "properties": [{"id": "displayName", "value": "Workload"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "links", "value": [{"targetBlank": true, "title": "Drill down", "url": "/d/3151475894614845ba54456099696738/k8s-resources-workload?var-datasource=$datasource&var-cluster=$cluster&var-namespace=$namespace&from=$__from&to=$__to&var-workload=$__cell&var-type=$__cell_2"}]}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "owner_kind"}, "properties": [{"id": "displayName", "value": "Workload Type"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "decimals", "value": 0}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 33}, "id": 89, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Memory Requests %"}]}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "count(\r\n    label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n", "format": "table", "instant": true, "legendFormat": "", "refId": "A"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(\r\n    container_memory_working_set_bytes{cluster=\"$cluster\", namespace=~\"$namespace\", container!=\"\", image!=\"\"}\r\n  * on(namespace,pod)\r\n      group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n", "format": "table", "instant": true, "legendFormat": "", "refId": "B"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(\r\n  kube_pod_container_resource_requests{cluster=\"$cluster\", namespace=~\"$namespace\", resource=\"memory\"}\r\n* on(namespace,pod)\r\n    group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n", "format": "table", "hide": false, "instant": true, "legendFormat": "", "refId": "C"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(\r\n    container_memory_working_set_bytes{cluster=\"$cluster\", namespace=~\"$namespace\", container!=\"\", image!=\"\"}\r\n  * on(namespace,pod)\r\n      group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n/sum(\r\n  kube_pod_container_resource_requests{cluster=\"$cluster\", namespace=~\"$namespace\", resource=\"memory\"}\r\n* on(namespace,pod)\r\n    group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n", "format": "table", "hide": false, "instant": true, "legendFormat": "", "refId": "D"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(\r\n  kube_pod_container_resource_limits{cluster=\"$cluster\", namespace=~\"$namespace\", resource=\"memory\"}\r\n* on(namespace,pod)\r\n    group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n", "format": "table", "instant": true, "legendFormat": "", "refId": "E"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(\r\n    container_memory_working_set_bytes{cluster=\"$cluster\", namespace=~\"$namespace\", container!=\"\", image!=\"\"}\r\n  * on(namespace,pod)\r\n      group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n/sum(\r\n  kube_pod_container_resource_limits{cluster=\"$cluster\", namespace=~\"$namespace\", resource=\"memory\"}\r\n* on(namespace,pod)\r\n    group_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name,owner_kind)\r\n", "format": "table", "instant": true, "legendFormat": "", "refId": "F"}], "title": "Memory Quota", "transformations": [{"id": "merge", "options": {"reducers": []}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}, "id": 30, "maxPerRow": 2, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "repeat": "datasource", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": true, "expr": "sum(container_memory_working_set_bytes{namespace=~\"$namespace\", image!=\"\", pod=~\"${created_by}.*\", cluster=\"$cluster\"}) by (pod)", "interval": "$resolution", "legendFormat": "{{ pod }}", "range": true, "refId": "A"}], "title": "Memory usage by Pod", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 48}, "id": 77, "panels": [], "title": "Network", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "decimals": 2, "displayName": "", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "displayName", "value": "Time"}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "Current Receive Bandwidth"}, {"id": "unit", "value": "Bps"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Current Transmit Bandwidth"}, {"id": "unit", "value": "Bps"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "displayName", "value": "Rate of Received Packets"}, {"id": "unit", "value": "pps"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #D"}, "properties": [{"id": "displayName", "value": "Rate of Transmitted Packets"}, {"id": "unit", "value": "pps"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #E"}, "properties": [{"id": "displayName", "value": "Rate of Received Packets Dropped"}, {"id": "unit", "value": "pps"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #F"}, "properties": [{"id": "displayName", "value": "Rate of Transmitted Packets Dropped"}, {"id": "unit", "value": "pps"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "owner_name"}, "properties": [{"id": "displayName", "value": "Workload"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "links", "value": [{"targetBlank": true, "title": "Drill down to pods", "url": "/d/3151475894614845ba54456099696738/k8s-resources-workload?var-datasource=$datasource&var-cluster=$cluster&var-namespace=$namespace&from=$__from&to=$__to&var-workload=$__cell&var-type=$type"}]}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "workload_type"}, "properties": [{"id": "displayName", "value": "Workload Type"}, {"id": "unit", "value": "short"}, {"id": "decimals", "value": 2}, {"id": "custom.align"}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 49}, "id": 90, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Rate of Transmitted Packets"}]}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(irate(container_network_receive_bytes_total{cluster=\"$cluster\", namespace=~\"$namespace\"}[$__rate_interval])\r\n* on (namespace,pod)\r\ngroup_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name)", "format": "table", "instant": true, "legendFormat": "", "refId": "A"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(irate(container_network_transmit_bytes_total{cluster=\"$cluster\", namespace=~\"$namespace\"}[$__rate_interval])\r\n* on (namespace,pod)\r\ngroup_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name)", "format": "table", "hide": false, "instant": true, "legendFormat": "", "refId": "B"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(irate(container_network_receive_packets_total{cluster=\"$cluster\", namespace=~\"$namespace\"}[$__rate_interval])\r\n* on (namespace,pod)\r\ngroup_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name)", "format": "table", "instant": true, "legendFormat": "", "refId": "C"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(irate(container_network_transmit_packets_total{cluster=\"$cluster\", namespace=~\"$namespace\"}[$__rate_interval])\r\n* on (namespace,pod)\r\ngroup_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name)", "format": "table", "instant": true, "legendFormat": "", "refId": "D"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(irate(container_network_receive_packets_dropped_total{cluster=\"$cluster\", namespace=~\"$namespace\"}[$__rate_interval])\r\n* on (namespace,pod)\r\ngroup_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name)", "format": "table", "instant": true, "legendFormat": "", "refId": "E"}, {"datasource": {"uid": "$datasource"}, "editorMode": "code", "expr": "sum(irate(container_network_transmit_packets_dropped_total{cluster=\"$cluster\", namespace=~\"$namespace\"}[$__rate_interval])\r\n* on (namespace,pod)\r\ngroup_left(owner_name,owner_kind) label_replace(kube_pod_owner{cluster=\"$cluster\",namespace=~\"$namespace\",owner_kind=~\"$type\"}* on(uid) group_left(node) kube_pod_owner\r\n    , \"owner_kind\",\"deployment\",\"owner_kind\",\"replicaset\")\r\n) by (owner_name)\r\n", "format": "table", "instant": true, "legendFormat": "", "refId": "F"}], "title": "Current Network Usage", "transformations": [{"id": "merge", "options": {"reducers": []}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 56}, "id": 78, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "scbdbeprometheus001dev"}, "editorMode": "code", "expr": "sum(irate(container_network_receive_bytes_total{cluster=\"$cluster\", namespace=~\"$namespace\"}[$__rate_interval])) by (pod)", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Receive Bandwidth", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 56}, "id": 80, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "scbdbeprometheus001dev"}, "editorMode": "code", "expr": "sum(irate(container_network_transmit_bytes_total{cluster=\"$cluster\", namespace=~\"$namespace\"}[$__rate_interval])) by (pod)", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Transmit Bandwidth", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 99, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "pps"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["nginx-controller-ingress-nginx-controller-kvhmr", "nginx-controller-ingress-nginx-defaultbackend-654b5746f4-9w5bn"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 64}, "id": 79, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(irate(container_network_receive_packets_total{cluster=\"$cluster\", namespace=~\"$namespace\"}[$__rate_interval])) by (pod)", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Rate of Received Packets", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 99, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "pps"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["nginx-controller-ingress-nginx-controller-kvhmr", "nginx-controller-ingress-nginx-defaultbackend-654b5746f4-9w5bn"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 64}, "id": 81, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(irate(container_network_transmit_packets_total{cluster=\"$cluster\", namespace=~\"$namespace\"}[$__rate_interval])) by (pod)", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Rate of Transmitted Packets", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "0 is generally a good thing here.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 99, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "pps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 72}, "id": 82, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(irate(container_network_receive_packets_dropped_total{cluster=\"$cluster\", namespace=~\"$namespace\"}[$__rate_interval])) by (pod)", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Rate of Received Packets Dropped", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "0 is generally a good thing here.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 99, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "pps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 72}, "id": 83, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(irate(container_network_transmit_packets_dropped_total{cluster=\"$cluster\", namespace=~\"$namespace\"}[$__rate_interval])) by (pod)", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Rate of Transmitted Packets Dropped", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 80}, "id": 73, "panels": [], "title": "Kubernetes", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 81}, "id": 70, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": true, "expr": "sum(kube_pod_status_qos_class{namespace=~\"$namespace\", cluster=\"$cluster\"}) by (qos_class)", "interval": "", "legendFormat": "{{ qos_class }} pods", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(kube_pod_info{namespace=~\"$namespace\", cluster=\"$cluster\"})", "hide": false, "legendFormat": "Total pods", "range": true, "refId": "B"}], "title": "Kubernetes Pods QoS classes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 81}, "id": 72, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": true, "expr": "sum(kube_pod_status_reason{cluster=\"$cluster\"}) by (reason)", "interval": "", "legendFormat": "{{ reason }}", "range": true, "refId": "A"}], "title": "Kubernetes Pods Status Reason", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 90}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(kube_pod_container_status_ready{namespace=~\"$namespace\", pod=~\"${created_by}.*\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Ready", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(kube_pod_container_status_running{namespace=~\"$namespace\", pod=~\"${created_by}.*\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Running", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_pod_container_status_waiting{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Waiting", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_pod_container_status_restarts_total{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Restarts Total", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum(kube_pod_container_status_terminated{namespace=~\"$namespace\", cluster=\"$cluster\"})", "interval": "", "legendFormat": "Terminated", "refId": "E"}], "title": "Nb of pods by state", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 90}, "id": 76, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "sum(kube_pod_container_info{namespace=~\"$namespace\", pod=~\"${created_by}.*\", cluster=\"$cluster\"}) by (pod)", "interval": "", "legendFormat": "{{ pod }}", "range": true, "refId": "A"}], "title": "Nb of containers by pod", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 98}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": true, "expr": "sum(kube_deployment_status_replicas_available{namespace=~\"$namespace\", cluster=\"$cluster\"}) by (deployment)", "interval": "", "legendFormat": "{{ deployment }}", "range": true, "refId": "A"}], "title": "Replicas available by deployment", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 98}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "expr": "kube_deployment_spec_replicas{cluster=\"$cluster\",namespace=~\"$namespace\"} - kube_deployment_status_replicas_available{cluster=\"$cluster\",namespace=~\"$namespace\"}", "interval": "", "legendFormat": "{{ deployment }}", "range": true, "refId": "A"}], "title": "Replicas unavailable by deployment", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "description": "No data is generally a good thing here.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "points", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 106}, "id": 75, "maxPerRow": 2, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.3.3", "repeat": "datasource", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(kube_pod_container_status_restarts_total{namespace=~\"${namespace}\", cluster=\"$cluster\"}[$__rate_interval])) by (namespace, pod) > 0", "interval": "", "legendFormat": "namespace: {{ namespace }} - pod: {{ pod }}", "range": true, "refId": "A"}], "title": "Container Restarts by namespace, pod", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 115}, "id": 42, "panels": [], "title": "Kubernetes Storage", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 116}, "id": 65, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "editorMode": "code", "exemplar": true, "expr": "sum(kubelet_volume_stats_used_bytes{namespace=~\"$namespace\", cluster=\"$cluster\"}) by (persistentvolumeclaim) / sum(kubelet_volume_stats_capacity_bytes{namespace=~\"$namespace\", cluster=\"$cluster\"}) by (persistentvolumeclaim)", "interval": "", "legendFormat": "{{ persistentvolumeclaim }}", "range": true, "refId": "A"}], "title": "Persistent Volumes - Capacity and usage in %", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 116}, "id": 66, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "exemplar": true, "expr": "sum(kubelet_volume_stats_used_bytes{namespace=~\"$namespace\", cluster=\"$cluster\"}) by (persistentvolumeclaim)", "interval": "", "legendFormat": "{{ persistentvolumeclaim }} - Used", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "exemplar": true, "expr": "sum(kubelet_volume_stats_capacity_bytes{namespace=~\"$namespace\", cluster=\"$cluster\"}) by (persistentvolumeclaim)", "hide": false, "interval": "", "legendFormat": "{{ persistentvolumeclaim }} - Capacity", "refId": "B"}], "title": "Persistent Volumes - Capacity and usage in bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 124}, "id": 27, "maxPerRow": 2, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "8.3.3", "repeat": "datasource", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "exemplar": true, "expr": "1 - sum(kubelet_volume_stats_inodes_used{namespace=~\"$namespace\", cluster=\"$cluster\"}) by (persistentvolumeclaim) / sum(kubelet_volume_stats_inodes{namespace=~\"$namespace\", cluster=\"$cluster\"}) by (persistentvolumeclaim)", "interval": "", "legendFormat": "{{ persistentvolumeclaim }}", "refId": "A"}], "title": "Persistent Volumes - Inodes", "type": "timeseries"}], "preload": false, "refresh": "30m", "schemaVersion": 41, "tags": ["Kubernetes", "Prometheus"], "templating": {"list": [{"current": {"text": "Managed_Prometheus_scbinfraprometheus001prd", "value": "scbinfraprometheus001prd"}, "includeAll": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "/infra|eapi/", "type": "datasource"}, {"current": {"text": "scb-prod-pci-01-aks", "value": "scb-prod-pci-01-aks"}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(kube_node_info,cluster)", "includeAll": false, "name": "cluster", "options": [], "query": {"qryType": 1, "query": "label_values(kube_node_info,cluster)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "/prod-pci|eapi/", "sort": 1, "type": "query"}, {"current": {"text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(kube_pod_info{cluster=\"$cluster\"}, namespace)", "includeAll": true, "multi": true, "name": "namespace", "options": [], "query": {"query": "label_values(kube_pod_info{cluster=\"$cluster\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/^eapi-.*/", "sort": 1, "type": "query"}, {"current": {"text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(kube_pod_owner{owner_kind!=\"\"}, owner_kind)", "includeAll": true, "multi": true, "name": "type", "options": [], "query": {"query": "label_values(kube_pod_owner{owner_kind!=\"\"}, owner_kind)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "type": "query"}, {"current": {"text": "30s", "value": "30s"}, "includeAll": false, "name": "resolution", "options": [{"selected": false, "text": "1s", "value": "1s"}, {"selected": false, "text": "15s", "value": "15s"}, {"selected": true, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "3m", "value": "3m"}, {"selected": false, "text": "5m", "value": "5m"}], "query": "1s, 15s, 30s, 1m, 3m, 5m", "type": "custom"}, {"current": {"text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "$datasource"}, "definition": "label_values(kube_pod_info{namespace=~\"$namespace\", cluster=\"$cluster\"},created_by_name)", "description": "Can be used to filter on a specific deployment, statefulset or deamonset (only relevant panels).", "includeAll": true, "multi": true, "name": "created_by", "options": [], "query": {"query": "label_values(kube_pod_info{namespace=~\"$namespace\", cluster=\"$cluster\"},created_by_name)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 2, "regex": "", "sort": 1, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Kubernetes / Views / Namespaces", "uid": "eapi_namespace", "version": 3}