
API Log viewer   tag: latest commit: a439c3e build time: 20240129_080840
Microservice Log
Gateway Log
Routing Details
total hit:22

Microservice Result for X-B3-TraceId: faa52a6c96f9d284
Time 	Messages 	Request UID 	Span ID 	App name 	Thread Name 	Logger name 	Level
2025-11-04 13:46:36.759 	

-->(api): 

		faa52a6c96f9d284 	api-framework-netflix-zuul 	http-nio-8080-exec-57 	com.scb.api.common.framework.config2.ServiceApiConfig 	INFO
2025-11-04 13:46:36.760 	

Incoming requestUID 25110413463666728127 - POST http://startbiz-api.scb.co.th/scb/rest/ent-api/v1/support/utility/proxy with QueryString = profile=DBD

		41152f419011aca8 	api-framework-netflix-zuul 	http-nio-8080-exec-57 	com.scb.api.common.netflix.zuul.filters.ZuulPreFilter 	INFO
2025-11-04 13:46:36.761 	

-->(ribbon): 

		41152f419011aca8 	api-framework-netflix-zuul 	http-nio-8080-exec-57 	com.scb.api.common.netflix.zuul.custom.CustomRibbonLoadBalancingHttpClient 	INFO
2025-11-04 13:46:36.764 	

Validation Interceptor ==> Pre-handle

		41152f419011aca8 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.common.framework.interceptor.CommonValidationInterceptor 	INFO
2025-11-04 13:46:36.765 	

unescapeString: {"OrganizationJuristicID": "0305568007796"}

		e9e01366ba722820 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.ent.support.util.proxy.impl.ProxyAPIImpl 	INFO
2025-11-04 13:46:36.765 	

[100.96.6.216] POST http://100.96.6.216:8080/scb/rest/ent-api/v1/support/utility/proxy?profile=DBD INITIATED...

		e9e01366ba722820 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.ent.support.util.proxy.controller.ProxyAPI 	INFO
2025-11-04 13:46:36.766 	

replace vault key = \$\{vault.authen_key}

		e9e01366ba722820 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.ent.support.util.proxy.util.MapSettingForRedisVaults 	INFO
2025-11-04 13:46:36.766 	

get profile vault = DBD

		e9e01366ba722820 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.ent.support.util.proxy.config.VaultInitial 	INFO
2025-11-04 13:46:36.766 	

Query Redis key: scb.security_injector.DBD.OAuth2.accessToken

		e9e01366ba722820 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.ent.support.util.proxy.util.RedisUtil 	INFO
2025-11-04 13:46:36.794 	

Query Redis key: scb.security_injector.DBD.OAuth2.tokenType

		e9e01366ba722820 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.ent.support.util.proxy.util.RedisUtil 	INFO
2025-11-04 13:46:36.794 	

Got Redis value from =====> Redis server : null

		e9e01366ba722820 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.ent.support.util.proxy.util.RedisUtil 	INFO
2025-11-04 13:46:36.823 	

Got Redis value from =====> Redis server : null

		e9e01366ba722820 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.ent.support.util.proxy.util.RedisUtil 	INFO
2025-11-04 13:46:36.824 	

Calling to GW Proxy(https://intapigw.scb.co.th:8448/scb/rest/ent-api/reverse/proxy) with requestUID 25110413463666728127

		e9e01366ba722820 	support-util-proxy 	hystrix-GatewayProxyClient-197 	com.scb.api.ent.support.util.proxy.service.GatewayProxyClient 	INFO
2025-11-04 13:46:37.090 	

Invalid cookie header: "Set-Cookie: visid_incap_3048096=sZ+TQRgGQ5eimtWyLQifVE2hCWkAAAAAQUIPAAAAAAB1YtkcxXyco6/DpmyZggG9; expires=Tue, 03 Nov 2026 16:32:51 GMT; HttpOnly; Path=/scb/rest/ent-api/reverse; Domain=intapigw.scb.co.th". Invalid 'expires' attribute: Tue, 03 Nov 2026 16:32:51 GMT

		5772a2c544e44dc8 	support-util-proxy 	hystrix-GatewayProxyClient-197 	org.apache.http.client.protocol.ResponseProcessCookies 	WARN
2025-11-04 13:46:37.091 	

Response Authen Error: {"status.description":"Invalid authorization credentials","status.code":"9500"}

		e9e01366ba722820 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.ent.support.util.proxy.service.custom.OAuth2HandleVaultSecurityInjector 	ERROR
2025-11-04 13:46:37.091 	

Handling Error Response for class java.lang.RuntimeException
Error Message: Response Authen Error: {}

		e9e01366ba722820 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.common.framework.controller.exception.CustomRESTExceptionController 	INFO
2025-11-04 13:46:37.092 	

Validation Interceptor ==> After-completion

		41152f419011aca8 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.common.framework.interceptor.CommonValidationInterceptor 	INFO
2025-11-04 13:46:37.092 	

Found uncaught exception
java.lang.RuntimeException: Response Authen Error: {}

	at com.scb.api.ent.support.util.proxy.service.custom.OAuth2HandleVaultSecurityInjector.filterFieldResponseAuthen(OAuth2HandleVaultSecurityInjector.java:200)

	at com.scb.api.ent.support.util.proxy.service.custom.OAuth2HandleVaultSecurityInjector.refreshToken(OAuth2HandleVaultSecurityInjector.java:158)

	at com.scb.api.ent.support.util.proxy.service.custom.OAuth2HandleVaultSecurityInjector.executeRequest(OAuth2HandleVaultSecurityInjector.java:93)

	at com.scb.api.ent.support.util.proxy.impl.ProxyAPIImpl.processProxy(ProxyAPIImpl.java:78)

	at com.scb.api.ent.support.util.proxy.controller.ProxyAPI.processProxy(ProxyAPI.java:92)

	at com.scb.api.ent.support.util.proxy.controller.ProxyAPI$$FastClassBySpringCGLIB$$c01b3031.invoke(<generated>)

	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)

	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:720)

	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:157)

	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:52)

	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:168)

	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)

	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:179)

	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:655)

	at com.scb.api.ent.support.util.proxy.controller.ProxyAPI$$EnhancerBySpringCGLIB$$f59c3c92.processProxy(<generated>)

	at sun.reflect.GeneratedMethodAccessor228.invoke(Unknown Source)

	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)

	at java.lang.reflect.Method.invoke(Method.java:498)

	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:221)

	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:136)

	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:114)

	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:827)

	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:738)

	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:85)

	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:963)

	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:897)

	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:970)

	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:872)

	at javax.servlet.http.HttpServlet.service(HttpServlet.java:648)

	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:846)

	at javax.servlet.http.HttpServlet.service(HttpServlet.java:729)

	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:230)

	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)

	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)

	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)

	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)

	at org.springframework.boot.web.filter.ApplicationContextHeaderFilter.doFilterInternal(ApplicationContextHeaderFilter.java:55)

	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)

	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)

	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)

	at org.springframework.boot.actuate.trace.WebRequestTraceFilter.doFilterInternal(WebRequestTraceFilter.java:105)

	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)

	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)

	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)

	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)

	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)

	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)

	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)

	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:87)

	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)

	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)

	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)

	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:77)

	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)

	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)

	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)

	at org.springframework.cloud.sleuth.instrument.web.TraceFilter.doFilter(TraceFilter.java:140)

	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)

	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)

	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:197)

	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)

	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)

	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)

	at org.springframework.boot.actuate.autoconfigure.MetricsFilter.doFilterInternal(MetricsFilter.java:107)

	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)

	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:192)

	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:165)

	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198)

	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:108)

	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:522)

	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)

	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:79)

	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)

	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:349)

	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:1110)

	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)

	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:785)

	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1425)

	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)

	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)

	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)

	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)

	at java.lang.Thread.run(Thread.java:750)

		e9e01366ba722820 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.common.framework.controller.exception.CustomRESTExceptionController 	ERROR
2025-11-04 13:46:37.092 	

Unknown exception type: java.lang.RuntimeException

		e9e01366ba722820 	support-util-proxy 	http-nio-8080-exec-1 	com.scb.api.common.framework.controller.exception.CustomRESTExceptionController 	WARN
2025-11-04 13:46:37.093 	

Parsed JSON data2

traceId: faa52a6c96f9d284
responsePayload:
{
    "errors": [
        {
            "code": "FW000",
            "message": "GENERAL API ERROR",
            "severitylevel": "ERROR",
            "description": "GENERAL API ERROR",
            "moreInfo": "Please contact the SCB API team for support."
        }
    ]
}
guessRoute: /scb/rest/ent-api/v1/support/utility/proxy

requestQuery: profile=DBD
isFileUpload: 
requestUID: 25110413463666728127
isContainSensitiveInformation: 
requestMethod: POST
requestPath: /scb/rest/ent-api/v1/support/utility/proxy
requestPayload:
{
    "body": "{\"OrganizationJuristicID\": \"0305568007796\"}",
    "method": "POST",
    "header": {},
    "url": "https://api.dbd.go.th/text/JuristicPerson/v1/InquiryOJPbyID"
}
responseCode: 500
processingTime: 333
routedServiceId: support-util-proxy
sourceSystem: OBJU

	25110413463666728127 	faa52a6c96f9d284 	api-framework-netflix-zuul 	http-nio-8080-exec-57 	com.scb.api.common.netflix.zuul.elk.ZuulStat 	INFO
2025-11-04 13:46:37.093 	

<--(ribbon): 

		41152f419011aca8 	api-framework-netflix-zuul 	http-nio-8080-exec-57 	com.scb.api.common.netflix.zuul.custom.CustomRibbonLoadBalancingHttpClient 	INFO
2025-11-04 13:46:37.093 	

<--(api): 

		faa52a6c96f9d284 	api-framework-netflix-zuul 	http-nio-8080-exec-57 	com.scb.api.common.framework.config2.ServiceApiConfig 	INFO

